#include <iostream>
#include <thread>
#include <chrono>

int main() {
    std::cout << "CallbackProto Demo Program" << std::endl;
    std::cout << "=========================" << std::endl;
    std::cout << "This project demonstrates cross-process callback mechanism." << std::endl;
    std::cout << std::endl;
    std::cout << "Usage:" << std::endl;
    std::cout << "1. First run in one terminal: ./process_b" << std::endl;
    std::cout << "2. Then run in another terminal: ./process_a" << std::endl;
    std::cout << std::endl;
    std::cout << "process_a will send user data to process_b," << std::endl;
    std::cout << "process_b will trigger callback function to handle the data." << std::endl;
    std::cout << std::endl;
    std::cout << "Press Enter to exit..." << std::endl;
    std::cin.get();

    return 0;
}