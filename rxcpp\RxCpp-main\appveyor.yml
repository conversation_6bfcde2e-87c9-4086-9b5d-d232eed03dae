version: 2.2.{build}

branches:
# whitelist
    only:
        - main
        - appveyor

image: Visual Studio 2017

environment:
  matrix:
  - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2015
    VSVER: Visual Studio 14 2015 Win64
  - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2017
    VSVER: Visual Studio 15 2017 Win64

platform:
  - x64

configuration:
  - Release

install:
    - cmake --version
    - git submodule -q update --init

before_build:
    - cmake -G"%VSVER%" .

build:
    project: build\rxcpp.sln

test_script:
  - cd build\test\
  - ctest -V -C Release
  - cd ..\..

artifacts:
  - path: Rx\v2\src\
    name: rxcpp source
    type: zip
  - path: Rx\v2\examples\
    name: rxcpp examples
    type: zip

notifications:
  - provider: Webhook
    url: https://webhooks.gitter.im/e/4c53c094183ccaa8d059
    method: POST
    on_build_success: true
    on_build_failure: true
    on_build_status_changed: false
  - provider: Email
    to:
      - <EMAIL>
    on_build_success: true
    on_build_failure: true
    on_build_status_changed: false
