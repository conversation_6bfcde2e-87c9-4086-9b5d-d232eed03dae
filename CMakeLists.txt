cmake_minimum_required(VERSION 3.8)
project(MyMultiProject LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 为所有子项目设置Qt路径
set(CMAKE_PREFIX_PATH "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64" CACHE STRING "Path to Qt installation")

# 添加所有子项目
add_subdirectory(pool)
add_subdirectory(Scheduler)
add_subdirectory(DesignPatternPractice)
add_subdirectory(algo)
add_subdirectory(callbackproto)