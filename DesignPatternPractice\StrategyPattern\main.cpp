#include <iostream>
#include "Calculator.h"
#include "ConcreteStrategies.h"

int main() {
    Calculator calculator;
    int a = 10;
    int b = 5;

    // 使用加法策略
    calculator.setStrategy(std::make_unique<AddStrategy>());
    std::cout << a << " + " << b << " = " << calculator.executeStrategy(a, b) << std::endl;

    // 在运行时轻松切换到减法策略
    calculator.setStrategy(std::make_unique<SubtractStrategy>());
    std::cout << a << " - " << b << " = " << calculator.executeStrategy(a, b) << std::endl;

    // 切换到乘法策略
    calculator.setStrategy(std::make_unique<MultiplyStrategy>());
    std::cout << a << " * " << b << " = " << calculator.executeStrategy(a, b) << std::endl;

    return 0;
}
