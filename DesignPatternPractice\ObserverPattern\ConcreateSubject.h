#pragma once
#include "ISubject.h"
#include <vector>
#include <string>
#include <algorithm>

class ConcreateSubject : public ISubject{

    public:
    void registerObserver(IObserver* observer) override{
        observers.push_back(observer);
    }
    void removeObserver(IObserver* observer) override{
        auto it  = std::remove(observers.begin(),observers.end(),observer);
        observers.erase(it,observers.end());
    }
    void notifyObserver() override{
        for(IObserver* observer : observers){
            observer->update(messages);
        }
    }
    void setState(const std::string& newMessage){
        messages = newMessage;
        notifyObserver();
    }
    private:
    std::vector <IObserver*> observers;
    std::string messages;
};