#include "ConcreteObserver.h"
#include "ConcreateSubject.h"
#include <iostream>
int main(){
    ConcreateSubject subject;
    ConcreateObserver obs1("Observer A");
    ConcreateObserver obs2("Observer B");

    subject.registerObserver(&obs1);
    subject.registerObserver(&obs2);

    std::cout << "--- Setting state to 'First Message' ---" << std::endl;
    subject.removeObserver(&obs1);

    std::cout << "\n--- Setting state to 'Second Message' ---" << std::endl;
    // subject.registerObserver(&obs1);
    subject.setState("Second Message");
    return 0;

}