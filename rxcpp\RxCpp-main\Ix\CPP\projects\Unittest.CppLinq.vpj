<!DOCTYPE Project SYSTEM "http://www.slickedit.com/dtd/vse/10.0/vpj.dtd">
<Project
	Version="10.0"
	VendorName="SlickEdit"
	TemplateName="(Other)"
	WorkingDir=".">
	<Config
		Name="Release"
		OutputFile=""
		CompilerConfigName="">
		<Menu>
			<Target
				Name="Compile"
				MenuCaption="&amp;Compile"
				CaptureOutputWith="ProcessBuffer"
				SaveOption="SaveCurrent"
				RunFromDir="%rw">
				<Exec/>
			</Target>
			<Target
				Name="Build"
				MenuCaption="&amp;Build"
				CaptureOutputWith="ProcessBuffer"
				SaveOption="SaveWorkspaceFiles"
				RunFromDir="%rw">
				<Exec CmdLine="nmake Config=%bn runtests"/>
			</Target>
			<Target
				Name="Rebuild"
				MenuCaption="&amp;Rebuild"
				CaptureOutputWith="ProcessBuffer"
				SaveOption="SaveWorkspaceFiles"
				RunFromDir="%rw">
				<Exec CmdLine="nmake Config=%bn clean all"/>
			</Target>
			<Target
				Name="Debug"
				MenuCaption="&amp;Debug"
				SaveOption="SaveNone"
				RunFromDir="%rw">
				<Exec/>
			</Target>
			<Target
				Name="Execute"
				MenuCaption="E&amp;xecute"
				SaveOption="SaveNone"
				RunFromDir="%rw">
				<Exec CmdLine='".exe"'/>
			</Target>
		</Menu>
	</Config>
	<Config
		Name="Debug"
		OutputFile=""
		CompilerConfigName="">
		<Menu>
			<Target
				Name="Compile"
				MenuCaption="&amp;Compile"
				CaptureOutputWith="ProcessBuffer"
				SaveOption="SaveCurrent"
				RunFromDir="%rw">
				<Exec/>
			</Target>
			<Target
				Name="Build"
				MenuCaption="&amp;Build"
				CaptureOutputWith="ProcessBuffer"
				SaveOption="SaveWorkspaceFiles"
				RunFromDir="%rw">
				<Exec CmdLine="nmake Config=%bn runtests"/>
			</Target>
			<Target
				Name="Rebuild"
				MenuCaption="&amp;Rebuild"
				CaptureOutputWith="ProcessBuffer"
				SaveOption="SaveWorkspaceFiles"
				RunFromDir="%rw">
				<Exec CmdLine="nmake Config=%bn clean all"/>
			</Target>
			<Target
				Name="Debug"
				MenuCaption="&amp;Debug"
				SaveOption="SaveNone"
				RunFromDir="%rw">
				<Exec/>
			</Target>
			<Target
				Name="Execute"
				MenuCaption="E&amp;xecute"
				SaveOption="SaveNone"
				RunFromDir="%rw">
				<Exec CmdLine='".exe"'/>
			</Target>
		</Menu>
	</Config>
	<CustomFolders>
		<Folder
			Name="Source Files"
			Filters="*.c;*.C;*.cc;*.cpp;*.cp;*.cxx;*.c++;*.prg;*.pas;*.dpr;*.asm;*.s;*.bas;*.java;*.cs;*.sc;*.e;*.cob;*.html;*.rc;*.tcl;*.py;*.pl;*.d">
		</Folder>
		<Folder
			Name="Header Files"
			Filters="*.h;*.H;*.hh;*.hpp;*.hxx;*.inc;*.sh;*.cpy;*.if">
		</Folder>
		<Folder
			Name="Resource Files"
			Filters="*.ico;*.cur;*.dlg"/>
		<Folder
			Name="Bitmaps"
			Filters="*.bmp"/>
		<Folder
			Name="Other Files"
			Filters="">
		</Folder>
	</CustomFolders>
	<Files AutoFolders="PackageView">
		<F
			N="*.cpp"
			Recurse="1"
			Refilter="0"
			Excludes=""/>
		<F
			N="*.h"
			Recurse="1"
			Refilter="0"
			Excludes=""/>
		<F
			N="*.hpp"
			Recurse="1"
			Refilter="0"
			Excludes=""/>
		<F
			N="makefile"
			Type="Makefile"/>
	</Files>
</Project>
