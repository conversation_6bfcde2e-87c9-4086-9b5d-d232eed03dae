#include <iostream>
#include <windows.h>
#include <vector>
#include <functional>

// 前向声明
class Scheduler;

// 协程（纤程）结构体
struct Coroutine {
    LPVOID fiber;               // Windows 纤程句柄
    bool is_finished;           // 是否执行完毕
    std::function<void()> func; // 协程任务
    Scheduler* scheduler;       // 指向调度器的指针

    Coroutine(std::function<void()> f, Scheduler* s) 
        : func(std::move(f)), is_finished(false), scheduler(s) {
        fiber = CreateFiber(0, FiberFunc, this);
    }

    ~Coroutine() {
        DeleteFiber(fiber);
    }

    static void WINAPI FiberFunc(LPVOID param);
};

// 调度器类（核心修改：加入循环调度）
class Scheduler {
public:
    LPVOID mainFiber;
    std::vector<Coroutine*> coroutines;
    int current = 0;  // 当前协程索引

    Scheduler() {
        mainFiber = ConvertThreadToFiber(nullptr);
    }

    ~Scheduler() {
        ConvertFiberToThread();
    }

    void add(Coroutine* co) {
        coroutines.push_back(co);
    }

    // 核心修改：循环调度所有未完成的协程
    void run() {
        while (true) {
            // 检查是否所有协程都已完成
            bool all_finished = true;
            for (auto* co : coroutines) {
                if (!co->is_finished) {
                    all_finished = false;
                    break;
                }
            }
            if (all_finished) break;  // 所有协程完成则退出循环

            // 找到下一个未完成的协程
            while (current < coroutines.size()) {
                auto* co = coroutines[current];
                current = (current + 1) % coroutines.size();  // 循环索引
                if (!co->is_finished) {
                    SwitchToFiber(co->fiber);  // 切换到该协程
                    break;
                }
            }
        }
    }
};

// 协程入口函数
void WINAPI Coroutine::FiberFunc(LPVOID param) {
    auto* co = static_cast<Coroutine*>(param);
    co->func();          // 执行用户任务
    co->is_finished = true;
    SwitchToFiber(co->scheduler->mainFiber);  // 切换回主纤程
}

int main() {
    Scheduler scheduler;

    // 协程1：执行3步
    auto co1 = new Coroutine([&]() {
        for (int i = 0; i < 3; ++i) {
            std::cout << "协程1 执行第" << i+1 << "步\n";
            SwitchToFiber(scheduler.mainFiber);  // 主动挂起，切换回主纤程
        }
    }, &scheduler);

    // 协程2：执行2步
    auto co2 = new Coroutine([&]() {
        for (int i = 0; i < 2; ++i) {
            std::cout << "协程2 执行第" << i+1 << "步\n";
            SwitchToFiber(scheduler.mainFiber);  // 主动挂起
        }
    }, &scheduler);

    // 协程3：执行4步
    auto co3 = new Coroutine([&]() {
        for (int i = 0; i < 4; ++i) {
            std::cout << "协程3 执行第" << i+1 << "步\n";
            SwitchToFiber(scheduler.mainFiber);  // 主动挂起
        }
    }, &scheduler);

    scheduler.add(co1);
    scheduler.add(co2);
    scheduler.add(co3);

    std::cout << "开始调度协程...\n";
    scheduler.run();
    std::cout << "所有协程执行完毕\n";

    delete co1;
    delete co2;
    delete co3;

    return 0;
}
    