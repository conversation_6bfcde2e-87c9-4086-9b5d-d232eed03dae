#include <iostream>
#include <fstream>
#include <stdexcept>
#include <string>

std::string readFile(const std::string &filename)
{
    if (filename.empty())
    {
        throw std::invalid_argument("filename is empty");
    }
    std::ifstream file(filename);
    if (!file.is_open())
    {
        throw std::runtime_error("file is not open" + filename);
    }
    return "read file success";
}

int main()
{
    int testCase;
    std::cin >> testCase;
    std::string filename;
    switch (testCase)
    {
    case 1:
        filename = "存在的文件.txt";
        break;
    case 2:
        filename = "";
        break;
    case 3:
        filename = "不存在的文件.txt";
        break;
    case 4:
        filename = "";
        break;
    default:
        std::cout << "无效选项" << std::endl;
        return 0;
    }
    try
    {
        std::cout << "\n========开始==========" << std::endl;
        std::string content = readFile(filename);
        std::cout << "========读取成功==========" << std::endl;
    }
    // 捕获"参数错误"（空文件名）
    catch (const std::invalid_argument &e)
    {
        std::cerr << "参数错误：" << e.what() << std::endl;
    }
    // 捕获"运行时错误"（文件不存在等）
    catch (const std::runtime_error &e)
    {
        std::cerr << "操作失败：" << e.what() << std::endl;
    }
    // 捕获所有其他错误
    catch (...)
    {
        std::cerr << "发生未知错误！" << std::endl;
    }

    // 无论是否出错，程序都会执行到这里
    std::cout << "\n=== 程序继续运行（测试结束） ===" << std::endl;
    return 0;
}