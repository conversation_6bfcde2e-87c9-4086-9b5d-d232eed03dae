#include "memorypool.h"

memorypool::memorypool(size_t blockSize, size_t numBlocks)
    : blockSize_(blockSize) {
  pool_ = new char[blockSize * numBlocks];
  for (size_t i = 0; i < numBlocks; ++i) {
    freeList_.push(pool_ + i * blockSize);
  }
}

memorypool::~memorypool() { delete[] pool_; }

void *memorypool::allocate() {
  std::lock_guard<std::mutex> lock(mutex_);
  if (freeList_.empty())
    return nullptr;
  void *block = freeList_.front();
  freeList_.pop();
  return block;
}

void memorypool::deallocate(void *block) {
  std::lock_guard<std::mutex> lock(mutex_);
  freeList_.push(static_cast<char *>(block));
}
