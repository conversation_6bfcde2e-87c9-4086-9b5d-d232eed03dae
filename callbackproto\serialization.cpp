#include "serialization.h"
#include <cstring>
#include <iostream>

std::vector<char> serialize(const User& user) {
    std::vector<char> data;

    // Serialize id
    const char* id_ptr = reinterpret_cast<const char*>(&user.id);
    data.insert(data.end(), id_ptr, id_ptr + sizeof(int));

    // Serialize name (length + content)
    int name_len = static_cast<int>(user.name.size());
    const char* len_ptr = reinterpret_cast<const char*>(&name_len);
    data.insert(data.end(), len_ptr, len_ptr + sizeof(int));
    data.insert(data.end(), user.name.begin(), user.name.end());

    return data;
}

User deserialize(const std::vector<char>& data) {
    int pos = 0;
    User user;

    // Deserialize id
    memcpy(&user.id, data.data() + pos, sizeof(int));
    pos += sizeof(int);

    // Deserialize name
    int name_len;
    memcpy(&name_len, data.data() + pos, sizeof(int));
    pos += sizeof(int);
    user.name = std::string(data.data() + pos, name_len);

    return user;
}
