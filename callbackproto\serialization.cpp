#include "serialization.h"
#include <cstring>

std::vector<char> serialize(const User& user) {
    std::vector<char> data;
    
    // 序列化id
    const char* id_ptr = reinterpret_cast<const char*>(&user.id);
    data.insert(data.end(), id_ptr, id_ptr + sizeof(int));
    
    // 序列化name（长度+内容）
    int name_len = user.name.size();
    const char* len_ptr = reinterpret_cast<const char*>(&name_len);
    data.insert(data.end(), len_ptr, len_ptr + sizeof(int));
    data.insert(data.end(), user.name.begin(), user.name.end());
    
    return data;
}

User deserialize(const std::vector<char>& data) {
    int pos = 0;
    User user;
    
    // 反序列化id
    memcpy(&user.id, data.data() + pos, sizeof(int));
    pos += sizeof(int);
    
    // 反序列化name
    int name_len;
    memcpy(&name_len, data.data() + pos, sizeof(int));
    pos += sizeof(int);
    user.name = std::string(data.data() + pos, name_len);
    
    return user;
}
