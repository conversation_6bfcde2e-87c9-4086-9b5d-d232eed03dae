#ifndef CONCRETE_STRATEGIES_H
#define CONCRETE_STRATEGIES_H

#include "IStrategy.h"

// 具体策略A：加法
class AddStrategy : public IStrategy {
public:
    int execute(int a, int b) const override {
        return a + b;
    }
};

// 具体策略B：减法
class SubtractStrategy : public IStrategy {
public:
    int execute(int a, int b) const override {
        return a - b;
    }
};

// 你可以轻松地在这里添加更多策略，例如乘法
class MultiplyStrategy : public IStrategy {
public:
    int execute(int a, int b) const override {
        return a * b;
    }
};

#endif // CONCRETE_STRATEGIES_H
