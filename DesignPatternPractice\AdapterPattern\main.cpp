#include <iostream>
#include <memory>
#include "ITarget.h"
#include "Adaptee.h"
#include "Adapter.h"

void clientCode(const ITarget* target){
    std::cout<<target->request()<<std::endl;
}

int main(){
    std::unique_ptr<Adaptee> adaptee = std::make_unique<Adaptee>();
    std::cout << adaptee->specificRequest() << std::endl;
    std::unique_ptr<ITarget> adapter = std::make_unique<Adapter>(std::move(adaptee));
    clientCode(adapter.get()); // 客户端通过 Target 接口与 Adapter 交互
    return 0;
}