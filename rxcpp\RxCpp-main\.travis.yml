language: cpp

dist: trusty
sudo: true

env:
  global:
    secure: FNZOWYO89qsT9kQKngrYbpnqCTMCSCSQ2rnZ3l17KHctOfS554TTDS+dfxPYz2XHm6azYDVcyBSs2gWJ6TmXZZZqEfr3X4VV4ooAa9PoeIkvVlT654zjZwxeXPYXrHFXZh/ImLUGWjsxRaUHYOv5SzyNGcZv07dtLKVd0ykhbH4=

matrix:
  include:

  - env: BUILD_TYPE=Debug ASAN=Off RUN_TEST=On EXCEPTIONS=On
    os: osx
    osx_image: xcode8

  - env: BUILD_TYPE=Debug ASAN=Off LLVM_VERSION=3.8.0 RUN_TEST=On EXCEPTIONS=On
    os: linux
    compiler: clang

  - env: BUILD_TYPE=Release ASAN=Off GCC_VERSION=4.9 RUN_TEST=On EXCEPTIONS=On
    os: linux
    compiler: gcc
    addons:
      apt:
        packages:
        - g++-4.9
        sources:
        - ubuntu-toolchain-r-test

  - env: BUILD_TYPE=Release ASAN=Off GCC_VERSION=7 RUN_TEST=On EXCEPTIONS=On
    os: linux
    compiler: gcc
    addons: &gcc7
      apt:
        packages:
          - g++-7
        sources:
          - ubuntu-toolchain-r-test

  - env: BUILD_TYPE=Debug ASAN=Off GCC_VERSION=8 RUN_TEST=On EXCEPTIONS=Off
    os: linux
    compiler: gcc
    addons: &gcc8
      apt:
        packages:
          - g++-8
        sources:
          - ubuntu-toolchain-r-test

  - env: BUILD_TYPE=Release ASAN=Off GCC_VERSION=8 RUN_TEST=On EXCEPTIONS=On
    os: linux
    compiler: gcc
    addons: &gcc8
      apt:
        packages:
          - g++-8
        sources:
          - ubuntu-toolchain-r-test

  - env: BUILD_TYPE=Debug ASAN=Off LLVM_VERSION=3.8.0 RUN_TEST=Off EXCEPTIONS=On PROJECT=doc PUBLISH_DOCS=On DOXYGEN_VERSION=1.8.11
    os: linux
    compiler: clang
    addons:
      apt:
        packages:
        - graphviz
        sources:
        - ubuntu-toolchain-r-test

cache:
  directories:
    - ${TRAVIS_BUILD_DIR}/deps/llvm-3.8.0
    - ${TRAVIS_BUILD_DIR}/deps/cmake-3.5.2
    - ${TRAVIS_BUILD_DIR}/deps/doxygen-1.8.11

install:
  ############################################################################
  # All the dependencies are installed in ${TRAVIS_BUILD_DIR}/deps/
  ############################################################################
  - DEPS_DIR="${TRAVIS_BUILD_DIR}/deps"
  - mkdir -p ${DEPS_DIR} && cd ${DEPS_DIR}

  ############################################################################
  # Install Clang, libc++ and libc++abi
  ############################################################################
  - |
    if [[ "${LLVM_VERSION}" != "" ]]; then
      LLVM_DIR=${DEPS_DIR}/llvm-${LLVM_VERSION}
      if [[ -z "$(ls -A ${LLVM_DIR})" ]]; then
        LLVM_URL="http://llvm.org/releases/${LLVM_VERSION}/llvm-${LLVM_VERSION}.src.tar.xz"
        LIBCXX_URL="http://llvm.org/releases/${LLVM_VERSION}/libcxx-${LLVM_VERSION}.src.tar.xz"
        LIBCXXABI_URL="http://llvm.org/releases/${LLVM_VERSION}/libcxxabi-${LLVM_VERSION}.src.tar.xz"
        CLANG_URL="http://llvm.org/releases/${LLVM_VERSION}/clang+llvm-${LLVM_VERSION}-x86_64-linux-gnu-ubuntu-14.04.tar.xz"
        mkdir -p ${LLVM_DIR} ${LLVM_DIR}/build ${LLVM_DIR}/projects/libcxx ${LLVM_DIR}/projects/libcxxabi ${LLVM_DIR}/clang
        travis_retry wget --quiet -O - ${LLVM_URL}      | tar --strip-components=1 -xJ -C ${LLVM_DIR}
        travis_retry wget --quiet -O - ${LIBCXX_URL}    | tar --strip-components=1 -xJ -C ${LLVM_DIR}/projects/libcxx
        travis_retry wget --quiet -O - ${LIBCXXABI_URL} | tar --strip-components=1 -xJ -C ${LLVM_DIR}/projects/libcxxabi
        travis_retry wget --quiet -O - ${CLANG_URL}     | tar --strip-components=1 -xJ -C ${LLVM_DIR}/clang
        (cd ${LLVM_DIR}/build && cmake .. -DCMAKE_INSTALL_PREFIX=${LLVM_DIR}/install -DCMAKE_CXX_COMPILER=clang++)
        (cd ${LLVM_DIR}/build/projects/libcxx && make install -j2)
        (cd ${LLVM_DIR}/build/projects/libcxxabi && make install -j2)
      fi
      export CXXFLAGS="-nostdinc++ -isystem ${LLVM_DIR}/install/include/c++/v1"
      export LDFLAGS="-L ${LLVM_DIR}/install/lib -l c++ -l c++abi"
      export LD_LIBRARY_PATH="${LD_LIBRARY_PATH}:${LLVM_DIR}/install/lib"
      export PATH="${LLVM_DIR}/clang/bin:${PATH}"
      $LLVM_DIR/clang/bin/clang++ --version
    fi

  ############################################################################
  # Use gcc
  ############################################################################
  - |
    if [ -n "$GCC_VERSION" ]; then
      export CXX="g++-${GCC_VERSION}" CC="gcc-${GCC_VERSION}";
      g++-${GCC_VERSION} --version
    fi

  - $CXX --version

  ############################################################################
  # Install a recent Doxygen
  ############################################################################
  - |
    if [ -n "$DOXYGEN_VERSION" ]; then
      DOXYGEN_DIR=${DEPS_DIR}/doxygen-${DOXYGEN_VERSION}
      if [[ -z "$(ls -A ${DOXYGEN_DIR})" ]]; then
        DOXYGEN_URL="https://downloads.sourceforge.net/doxygen/doxygen-${DOXYGEN_VERSION}.linux.bin.tar.gz"
        mkdir -p ${DOXYGEN_DIR} && travis_retry wget --quiet -O - ${DOXYGEN_URL} | tar --strip-components=1 -xz -C ${DOXYGEN_DIR}
      fi
      export PATH=${DOXYGEN_DIR}/bin:${PATH}
      doxygen --version
    fi

  ############################################################################
  # Install a recent CMake (unless already installed on OS X)
  ############################################################################
  - |
    if [ -z "$CMAKE_VERSION" ]; then
      CMAKE_VERSION=3.5.2;
    fi;
    if [[ "${TRAVIS_OS_NAME}" == "linux" ]]; then
      CMAKE_DIR=${DEPS_DIR}/cmake-${CMAKE_VERSION}
      if [[ -z "$(ls -A ${CMAKE_DIR})" ]]; then
        CMAKE_URL="http://www.cmake.org/files/v3.5/cmake-${CMAKE_VERSION}-Linux-x86_64.tar.gz"
        mkdir -p ${CMAKE_DIR} && travis_retry wget --no-check-certificate --quiet -O - ${CMAKE_URL} | tar --strip-components=1 -xz -C ${CMAKE_DIR}
      fi
      export PATH=${CMAKE_DIR}/bin:${PATH}
    else
      if ! brew ls --version cmake &>/dev/null; then brew install cmake; fi
    fi
    cmake --version

before_script:
  - |
    if [ -z "$BUILD_TYPE" ]; then
      BUILD_TYPE=Release;
    fi;
    if [ -z "$EXCEPTIONS" ]; then
      EXCEPTIONS=On;
    fi;
    if [[ "${ASAN}" == "On" ]]; then
      export CXXFLAGS="${CXXFLAGS} -fsanitize=address,undefined,integer -fno-omit-frame-pointer -fno-sanitize=unsigned-integer-overflow";
    fi;
    if [ -n "$LLVM_VERSION" ]; then
      export CXXFLAGS="${CXXFLAGS} -D__extern_always_inline=inline";
    fi;

  ############################################################################
  # generate build
  ############################################################################
  - cd ${TRAVIS_BUILD_DIR}
  - cmake . -DCMAKE_BUILD_TYPE=${BUILD_TYPE} -DRX_USE_EXCEPTIONS=${EXCEPTIONS}

script:
  ############################################################################
  # Run build
  ############################################################################
  - make VERBOSE=1 $PROJECT

  ############################################################################
  # Test build
  ############################################################################
  - cd build/test/
  - if [[ "${RUN_TEST}" == "On" ]]; then ctest -V; fi
  - cd ../../

after_success:
  ############################################################################
  # Publish documentation
  ############################################################################
  - |
    if [[ "${PUBLISH_DOCS}" == "On" ]]; then
      sh projects/scripts/travis-doxygen.sh;
    fi;

branches:
  only:
  - main

notifications:
  email:
    recipients:
      - <EMAIL>
    on_success: always
    on_failure: always
  slack: reactivex:eq4FFCchRIzufO2ZmpuuiJ9E
  webhooks:
    urls:
      - https://webhooks.gitter.im/e/fcf05f21f5d4102e12e7
deploy:
  - provider: releases
    api_key:
      secure: sMyo0U4WxZ0kOlQ9SzKfeuU2/5y0Ngt8A0B0N6bb0TcGIyLhQv2q216Q+T0rkQKc04WnLY0Vr1/q9LonAA46nGq7zAiWcC3TY0zVUKRmKWrotQb/QqW1tvVEARKwG7CO+HCcGT3XCeM69XA/L7WiXRskTxS1+5Vy46/tVZBKofg=
    file: ''
    on:
      tags: true
      repo: Reactive-Extensions/RxCpp
