﻿cmake_minimum_required(VERSION 3.10)
project(DesignPatternPractice CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

add_executable(FactoryPattern
    FactoryPattern/ITransport.h
    FactoryPattern/ItransportFactory.h
    FactoryPattern/ShipFactory.h
    FactoryPattern/TruckFactory.h
    FactoryPattern/main.cpp
    FactoryPattern/Truck.cpp
    FactoryPattern/Ship.cpp
)
target_include_directories(FactoryPattern PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}/FactoryPattern)
add_executable(SingletonPattern
    SingletonPattern/main.cpp
)
target_include_directories(SingletonPattern PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}/SingletonPattern)
add_executable(AdapterPattern
    AdapterPattern/main.cpp
    AdapterPattern/Adaptee.h
    AdapterPattern/Adapter.h
    AdapterPattern/ITarget.h
)
target_include_directories(AdapterPattern PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}/AdapterPattern)
add_executable(ObserverPattern
            ObserverPattern/main.cpp
        )
target_include_directories(ObserverPattern PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}/ObserverPattern)
add_executable(DecoratorPattern
            DecoratorPattern/main.cpp
        )
target_include_directories(DecoratorPattern PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}/DecoratorPattern)
