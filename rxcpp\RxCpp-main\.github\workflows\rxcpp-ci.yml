name: rxcpp CI

# Trigger on pushes to all branches and for all pull-requests
on: [push, pull_request]

env:
  CMAKE_VERSION: 3.16.2
  NINJA_VERSION: 1.9.0


jobs:
  build:
    name: ${{ matrix.config.name }}
    runs-on: ${{ matrix.config.os }}
    strategy:
      fail-fast: false
      matrix:
        config:
        - {
            name: "Linux GCC 9 Debug (C++11)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: Debug,
            cc: "gcc-9", cxx: "g++-9",
            cmake_args: "-D CMAKE_CXX_STANDARD:STRING=11"
          }
        - {
            name: "Linux GCC 9 Optimised (C++11)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: RelWithDebInfo,
            cc: "gcc-9", cxx: "g++-9",
            cmake_args: "-D CMAKE_CXX_STANDARD:STRING=11"
          }
        - {
            name: "Linux GCC 9 Debug (C++14)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: Debug,
            cc: "gcc-9", cxx: "g++-9",
            cmake_args: "-D CMAKE_CXX_STANDARD:STRING=14"
          }
        - {
            name: "Linux GCC 9 Optimised (C++14)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: RelWithDebInfo,
            cc: "gcc-9", cxx: "g++-9",
            cmake_args: "-D CMAKE_CXX_STANDARD:STRING=14"
          }
        - {
            name: "Linux GCC 9 Debug (C++17)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: Debug,
            cc: "gcc-9", cxx: "g++-9",
            cmake_args: "-D CMAKE_CXX_STANDARD:STRING=17"
          }
        - {
            name: "Linux GCC 9 Optimised (C++17)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: RelWithDebInfo,
            cc: "gcc-9", cxx: "g++-9",
            cmake_args: "-D CMAKE_CXX_STANDARD:STRING=17"
          }
        - {
            name: "macOS Clang Debug (C++17)", artifact: "macOS.tar.xz",
            os: macos-latest,
            build_type: Debug,
            cc: "clang", cxx: "clang++",
            cmake_args: "-D \"CMAKE_CXX_STANDARD:STRING=17\" -D \"CMAKE_CXX_FLAGS:STRING=-fsanitize=address -fno-omit-frame-pointer\""
          }
        - {
            name: "macOS Clang Optimised (C++17)", artifact: "macOS.tar.xz",
            os: macos-latest,
            build_type: RelWithDebInfo,
            cc: "clang", cxx: "clang++",
            cmake_args: "-D \"CMAKE_CXX_STANDARD:STRING=17\" -D \"CMAKE_CXX_FLAGS:STRING=-fsanitize=address -fno-omit-frame-pointer\""
          }
        - {
            name: "Windows MSVC 2019 Debug (C++17)", artifact: "Windows-MSVC.tar.xz",
            os: windows-latest,
            build_type: Debug,
            cc: "cl", cxx: "cl",
            environment_script: "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/VC/Auxiliary/Build/vcvars64.bat",
            cmake_args: "-D CMAKE_CXX_STANDARD:STRING=17",
            experimental: true
          }
        - {
            name: "Windows MSVC 2019 Optimised (C++17)", artifact: "Windows-MSVC.tar.xz",
            os: windows-latest,
            build_type: RelWithDebInfo,
            cc: "cl", cxx: "cl",
            environment_script: "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/VC/Auxiliary/Build/vcvars64.bat",
            cmake_args: "-D CMAKE_CXX_STANDARD:STRING=17",
            experimental: true
          }

    steps:
    - uses: actions/checkout@v1
      with:
        submodules: true

    - name: Download Ninja and CMake
      id: cmake_and_ninja
      shell: cmake -P {0}
      run: |
        set(cmake_version $ENV{CMAKE_VERSION})
        set(ninja_version $ENV{NINJA_VERSION})

        message(STATUS "Using host CMake version: ${CMAKE_VERSION}")

        if ("${{ runner.os }}" STREQUAL "Windows")
          set(ninja_suffix "win.zip")
          set(cmake_suffix "win64-x64.zip")
          set(cmake_dir "cmake-${cmake_version}-win64-x64/bin")
        elseif ("${{ runner.os }}" STREQUAL "Linux")
          set(ninja_suffix "linux.zip")
          set(cmake_suffix "Linux-x86_64.tar.gz")
          set(cmake_dir "cmake-${cmake_version}-Linux-x86_64/bin")
        elseif ("${{ runner.os }}" STREQUAL "macOS")
          set(ninja_suffix "mac.zip")
          set(cmake_suffix "Darwin-x86_64.tar.gz")
          set(cmake_dir "cmake-${cmake_version}-Darwin-x86_64/CMake.app/Contents/bin")
        endif()

        set(ninja_url "https://github.com/ninja-build/ninja/releases/download/v${ninja_version}/ninja-${ninja_suffix}")
        file(DOWNLOAD "${ninja_url}" ./ninja.zip SHOW_PROGRESS)
        execute_process(COMMAND ${CMAKE_COMMAND} -E tar xvf ./ninja.zip)

        set(cmake_url "https://github.com/Kitware/CMake/releases/download/v${cmake_version}/cmake-${cmake_version}-${cmake_suffix}")
        file(DOWNLOAD "${cmake_url}" ./cmake.zip SHOW_PROGRESS)
        execute_process(COMMAND ${CMAKE_COMMAND} -E tar xvf ./cmake.zip)

        # Save the path for other steps
        file(TO_CMAKE_PATH "$ENV{GITHUB_WORKSPACE}/${cmake_dir}" cmake_dir)
        message("::set-output name=cmake_dir::${cmake_dir}")

        if (NOT "${{ runner.os }}" STREQUAL "Windows")
          execute_process(
            COMMAND chmod +x ninja
            COMMAND chmod +x ${cmake_dir}/cmake
          )
        endif()

    - name: Install Clang 10
      id: install_clang_10
      if: startsWith(matrix.config.os, 'ubuntu') && ( matrix.config.cxx == 'clang++-10' )
      shell: bash
      working-directory: ${{ env.HOME }}
      run: |
        wget https://apt.llvm.org/llvm.sh
        chmod +x llvm.sh
        sudo ./llvm.sh 10

    - name: Install Clang 11
      id: install_clang_11
      if: startsWith(matrix.config.os, 'ubuntu') && ( matrix.config.cxx == 'clang++-11' )
      shell: bash
      working-directory: ${{ env.HOME }}
      run: |
        wget https://apt.llvm.org/llvm.sh
        chmod +x llvm.sh
        sudo ./llvm.sh 11

    - name: Install Clang 12
      id: install_clang_12
      if: startsWith(matrix.config.os, 'ubuntu') && ( matrix.config.cxx == 'clang++-12' )
      shell: bash
      working-directory: ${{ env.HOME }}
      run: |
        wget https://apt.llvm.org/llvm.sh
        chmod +x llvm.sh
        sudo ./llvm.sh 12

    - name: Configure
      shell: cmake -P {0}
      run: |
        set(ENV{CC} ${{ matrix.config.cc }})
        set(ENV{CXX} ${{ matrix.config.cxx }})

        if ("${{ runner.os }}" STREQUAL "Windows" AND NOT "x${{ matrix.config.environment_script }}" STREQUAL "x")
          execute_process(
            COMMAND "${{ matrix.config.environment_script }}" && set
            OUTPUT_FILE environment_script_output.txt
          )
          set(ENV{CXXFLAGS} "/permissive- /Zc:externConstexpr")
          file(STRINGS environment_script_output.txt output_lines)
          foreach(line IN LISTS output_lines)
            if (line MATCHES "^([a-zA-Z0-9_-]+)=(.*)$")
              set(ENV{${CMAKE_MATCH_1}} "${CMAKE_MATCH_2}")
            endif()
          endforeach()
        endif()

        set(path_separator ":")
        if ("${{ runner.os }}" STREQUAL "Windows")
          set(path_separator ";")
        endif()
        set(ENV{PATH} "$ENV{GITHUB_WORKSPACE}${path_separator}$ENV{PATH}")

        execute_process(
          COMMAND ${{ steps.cmake_and_ninja.outputs.cmake_dir }}/cmake
            -S .
            -B build
            -D CMAKE_BUILD_TYPE=${{ matrix.config.build_type }}
            -G Ninja
            -D CMAKE_MAKE_PROGRAM=ninja
            -D CMAKE_VERBOSE_MAKEFILE=ON
            ${{ matrix.config.cmake_args }}
          RESULT_VARIABLE result
        )
        if (NOT result EQUAL 0)
          message(FATAL_ERROR "Bad exit status")
        endif()

    - name: Build
      shell: cmake -P {0}
      continue-on-error: ${{ matrix.config.experimental || false }}
      run: |
        set(ENV{NINJA_STATUS} "[%f/%t %o/sec] ")

        if ("${{ runner.os }}" STREQUAL "Windows" AND NOT "x${{ matrix.config.environment_script }}" STREQUAL "x")
          file(STRINGS environment_script_output.txt output_lines)
          foreach(line IN LISTS output_lines)
            if (line MATCHES "^([a-zA-Z0-9_-]+)=(.*)$")
              set(ENV{${CMAKE_MATCH_1}} "${CMAKE_MATCH_2}")
            endif()
          endforeach()
        endif()

        set(path_separator ":")
        if ("${{ runner.os }}" STREQUAL "Windows")
          set(path_separator ";")
        endif()
        set(ENV{PATH} "$ENV{GITHUB_WORKSPACE}${path_separator}$ENV{PATH}")

        execute_process(
          COMMAND ${{ steps.cmake_and_ninja.outputs.cmake_dir }}/cmake --build build
          RESULT_VARIABLE result
        )
        if (NOT result EQUAL 0)
          message(FATAL_ERROR "Bad exit status")
        endif()

    - name: Run tests
      shell: cmake -P {0}
      continue-on-error: ${{ matrix.config.experimental || false }}
      run: |
        include(ProcessorCount)
        ProcessorCount(N)

        set(ENV{CTEST_OUTPUT_ON_FAILURE} "ON")

        execute_process(
          COMMAND ${{ steps.cmake_and_ninja.outputs.cmake_dir }}/ctest --verbose -j ${N}
          WORKING_DIRECTORY build
          RESULT_VARIABLE result
        )
        if (NOT result EQUAL 0)
          message(FATAL_ERROR "Running tests failed!")
        endif()
