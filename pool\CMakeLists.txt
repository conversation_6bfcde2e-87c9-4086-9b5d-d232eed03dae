cmake_minimum_required(VERSION 3.5)

project(pool LANGUAGES CXX)

set(CMAKE_INCLUDE_CURRENT_DIR ON)

set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)


set(CMAKE_PREFIX_PATH "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64")
find_package(Qt5 COMPONENTS Core Quick REQUIRED)

if(ANDROID)
    add_library(pool SHARED main.cpp qml.qrc)
else()
    add_executable(pool main.cpp memorypool.h qml.qrc
        threadpool.h threadpool.cpp
        memorypool.cpp
        processDataBlock.cpp)
endif()

target_compile_definitions(pool
  PRIVATE $<$<OR:$<CONFIG:Debug>,$<CONFIG:RelWithDebInfo>>:QT_QML_DEBUG>)
target_link_libraries(pool
  PRIVATE Qt5::Core Qt5::Quick)

# QtCreator supports the following variables for Android, which are identical to qmake Android variables.
# Check http://doc.qt.io/qt-5/deployment-android.html for more information.
# These variables must use CACHE, otherwise <PERSON><PERSON><PERSON><PERSON> won't see them.

#if(ANDROID)
#    set(ANDROID_PACKAGE_SOURCE_DIR "${CMAKE_CURRENT_SOURCE_DIR}/android" CACHE INTERNAL "")
#    if (ANDROID_ABI STREQUAL "armeabi-v7a")
#        set(ANDROID_EXTRA_LIBS ${CMAKE_CURRENT_SOURCE_DIR}/path/to/libcrypto.so ${CMAKE_CURRENT_SOURCE_DIR}/path/to/libssl.so CACHE INTERNAL "")
#    endif()
#endif()
