#include "SimpleCoffee.h"
#include "MilkeDecorator.h"
#include "SugarDecorator.h"
#include<iostream>
#include <memory>

int main(){
    std::unique_ptr<ICoffee> myCoffee  = std::make_unique<SimpelCoffee>();
    std::cout << "Order: " << myCoffee->getDescription()
                << ", Cost: $" << myCoffee->getCost() << std::endl;

    myCoffee = std::make_unique<MilkeDecorator>(myCoffee.release());
    std::cout << "Order: " << myCoffee->getDescription()
                 << ", Cost: $" << myCoffee->getCost() << std::endl;

    myCoffee = std::make_unique<SurgarDecorator>(myCoffee.release());
       std::cout << "Order: " << myCoffee->getDescription()
                  << ", Cost: $" << myCoffee->getCost() << std::endl;
  
        // 你也可以一次性完成所有装饰
       std::cout << "\n--- New Order ---" << std::endl;
        std::unique_ptr<ICoffee> anotherCoffee = std::make_unique<SimpelCoffee>();
        anotherCoffee = std::make_unique<SurgarDecorator>(
                            std::make_unique<MilkeDecorator>(anotherCoffee.release()).release()
                      );
        std::cout << "Order: " << anotherCoffee->getDescription()
                << ", Cost: $" << anotherCoffee->getCost() << std::endl;
   
   
        return 0;
}