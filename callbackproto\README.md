# CallbackProto - 跨进程回调演示

这个项目演示了如何在不同进程之间实现回调机制。项目支持两种模式：
- **ZeroMQ 模式**：使用 ZeroMQ 进行网络通信（如果安装了 ZeroMQ）
- **文件模式**：使用文件进行进程间通信（简化版本，无需额外依赖）

## 项目结构

- `common.h` - 定义用户数据结构和回调函数类型
- `serialization.h/cpp` - 数据序列化和反序列化功能
- `process_a.cpp` - 发送方进程，生成用户数据并发送
- `process_b.cpp` - 接收方进程，接收数据并触发回调
- `main.cpp` - 演示程序说明

## 依赖项

### 必需
- C++17 或更高版本
- CMake 3.5 或更高版本

### 可选（用于网络通信）
- ZeroMQ 库

## 安装 ZeroMQ（可选）

### Windows (使用 vcpkg)
```bash
vcpkg install zeromq
```

### Ubuntu/Debian
```bash
sudo apt-get install libzmq3-dev
```

### macOS (使用 Homebrew)
```bash
brew install zeromq
```

## 构建项目

```bash
# 创建构建目录
mkdir build
cd build

# 配置项目
cmake ..

# 编译
cmake --build .
```

## 运行演示

### 方法一：简单测试（推荐）
1. **运行发送方进程（process_a）**：
   ```bash
   ./process_a
   ```
   这会创建用户数据并保存到文件。

2. **运行接收方进程（process_b）**：
   ```bash
   ./process_b
   ```
   这会读取文件数据并触发回调处理。

### 方法二：模拟实时通信
1. **首先运行接收方进程（process_b）**：
   ```bash
   ./process_b
   ```
   这个进程会等待接收数据（最多30秒）。

2. **然后在另一个终端运行发送方进程（process_a）**：
   ```bash
   ./process_a
   ```
   这个进程会发送用户数据给 process_b。

3. **查看演示说明**：
   ```bash
   ./main
   ```

## 工作原理

### 文件模式（当前默认）
1. `process_a` 创建一个 User 对象，将其序列化为字节流
2. 将数据写入 `user_data.bin` 文件
3. `process_b` 监控并读取文件，反序列化为 User 对象
4. `process_b` 触发注册的回调函数处理接收到的用户数据

### ZeroMQ 模式（如果安装了 ZeroMQ）
1. `process_a` 创建一个 User 对象，将其序列化为字节流
2. 通过 ZeroMQ 的 PUSH 套接字发送数据到 `process_b`
3. `process_b` 通过 PULL 套接字接收数据，反序列化为 User 对象
4. `process_b` 触发注册的回调函数处理接收到的用户数据

## 故障排除

### 编译问题
- 确保使用 C++17 或更高版本的编译器
- 如果遇到编码问题，确保源文件使用 UTF-8 编码

### ZeroMQ 相关问题
如果想使用 ZeroMQ 模式但遇到问题：
1. 确保已正确安装 ZeroMQ
2. 如果使用 vcpkg，确保 CMakeLists.txt 中的路径正确
3. 可以设置环境变量 `CMAKE_PREFIX_PATH` 指向 ZeroMQ 安装目录

### 运行时问题
- 如果 process_b 找不到数据文件，确保先运行 process_a
- 在 Windows 上，可执行文件位于 `build/Debug/` 目录中

## 测试结果示例

```
> .\Debug\process_a.exe
Process A sending data: id=1001, name=Alice
Data written to user_data.bin file
Run process_b to read and process the data

> .\Debug\process_b.exe
Process B waiting for data file...
Data file found and read
Process B callback handling: received user id=1001, name=Alice
```

## 扩展

这个基础框架可以扩展为：
- 支持更复杂的数据结构
- 双向通信
- 多个回调函数
- 错误处理和重连机制
- 异步处理
- 多线程支持
