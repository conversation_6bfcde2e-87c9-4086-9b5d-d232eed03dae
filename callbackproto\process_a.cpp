#include "common.h"
#include "serialization.h"
#include <iostream>
#include <vector>

#ifdef USE_ZMQ
#include <zmq.h> // Use zmq C API for cross-process communication
#else
#include <fstream>
#endif

// Process A: Generate User and send to Process B
int main() {
    std::cout << "--- Process A (Sender) ---" << std::endl;

    // Create User object
    User user{1001, "Alice"};
    
    // Serialize User
    std::vector<char> data = serialize(user);

#ifdef USE_ZMQ
    std::cout << "[Mode]: ZeroMQ (tcp://localhost:5555)" << std::endl;
    std::cout << "Sending data: id=" << user.id << ", name=" << user.name << std::endl;

    void* context = zmq_ctx_new();
    void* socket = zmq_socket(context, ZMQ_PUSH);
    zmq_connect(socket, "tcp://localhost:5555");

    // Send data
    zmq_send(socket, data.data(), data.size(), 0);
    std::cout << "Data sent successfully." << std::endl;
    std::cout << "(Note: Run process_b before running process_a)" << std::endl;

    // Cleanup
    zmq_close(socket);
    zmq_ctx_destroy(context);
#else
    std::cout << "[Mode]: File I/O (user_data.bin)" << std::endl;
    std::cout << "Writing data: id=" << user.id << ", name=" << user.name << std::endl;

    std::ofstream file("user_data.bin", std::ios::binary);
    if (file.is_open()) {
        file.write(data.data(), data.size());
        file.close();
        std::cout << "Data written successfully to 'user_data.bin'." << std::endl;
        std::cout << "\nNext step: Run process_b to read the file." << std::endl;
    } else {
        std::cerr << "Error: Failed to open file for writing." << std::endl;
        return 1;
    }
#endif

    return 0;
}