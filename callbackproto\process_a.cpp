#include "common.h"
#include "serialization.h"
#include <zmq.hpp> // 使用zmq进行跨进程通信

// 进程A：生成User并发送给进程B
int main() {
    zmq::context_t context(1);
    zmq::socket_t socket(context, zmq::socket_type::push);
    socket.connect("tcp://localhost:5555"); // 连接到进程B
    
    // 创建User对象
    User user{1001, "Alice"};
    std::cout << "进程A发送数据：id=" << user.id << ", name=" << user.name << std::endl;
    
    // 序列化User
    std::vector<char> data = serialize(user);
    
    // 发送数据（通过网络传递给进程B，触发其回调）
    zmq::message_t msg(data.size());
    memcpy(msg.data(), data.data(), data.size());
    socket.send(msg, zmq::send_flags::none);
    
    return 0;
}
