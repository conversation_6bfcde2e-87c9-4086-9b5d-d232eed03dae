cmake_minimum_required(VERSION 3.5)
project(callbackproto LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找 ZeroMQ 库
# 你需要确保已经通过 vcpkg, brew, apt-get 或其他方式安装了 zeromq
set(ZeroMQ_DIR "E:/software/zmq/vcpkg-2025.07.25/installed/x64-windows/")
find_package(ZeroMQ REQUIRED)

# 添加序列化逻辑的源文件
add_library(serialization STATIC serialization.cpp)
# 让其他目标可以链接到这个库
target_include_directories(serialization PUBLIC ${CMAKE_CURRENT_SOURCE_DIR})

# 创建 process_a 程序
add_executable(process_a process_a.cpp)
target_include_directories(process_a PRIVATE "E:/software/zmq/vcpkg-2025.07.25/installed/x64-windows/include")
target_link_libraries(process_a PRIVATE serialization libzmq)

# 创建 process_b 程序
add_executable(process_b process_b.cpp)
target_include_directories(process_b PRIVATE "E:/software/zmq/vcpkg-2025.07.25/installed/x64-windows/include")
target_link_libraries(process_b PRIVATE serialization libzmq)