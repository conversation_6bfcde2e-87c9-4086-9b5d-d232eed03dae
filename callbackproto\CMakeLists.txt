cmake_minimum_required(VERSION 3.8)
project(callbackproto LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# --- 通信方式开关 ---
# 提供一个CMake选项来控制是否使用ZeroMQ，默认为ON（开启）
option(USE_ZMQ "Enable ZeroMQ for Inter-Process Communication" ON)

# 添加序列化逻辑的库 (所有模式都需要)
add_library(serialization STATIC serialization.cpp)
target_include_directories(serialization PUBLIC ${CMAKE_CURRENT_SOURCE_DIR})

# --- 根据开关配置不同的依赖和编译定义 ---
if(USE_ZMQ)
    # 如果USE_ZMQ为ON，则添加ZMQ相关的编译定义和依赖
    message(STATUS "Building with ZeroMQ communication enabled.")
    
    # 1. 添加宏定义
    target_compile_definitions(serialization PUBLIC USE_ZMQ)

    # 2. 查找ZeroMQ库 (硬编码路径以确保找到)
    set(ZeroMQ_DIR "E:/software/zmq/vcpkg-2025.07.25/installed/x64-windows/share/zeromq/")
    find_package(ZeroMQ CONFIG REQUIRED)

    # 3. 定义ZeroMQ的头文件目录 (硬编码路径以确保找到)
    set(ZMQ_INCLUDE_DIR "E:/software/zmq/vcpkg-2025.07.25/installed/x64-windows/include")

else()
    # 如果USE_ZMQ为OFF，则不添加ZMQ依赖
    message(STATUS "Building with File I/O communication enabled.")
endif()


# --- 创建可执行文件 ---

# 创建 process_a 程序
add_executable(process_a process_a.cpp)
target_link_libraries(process_a PRIVATE serialization) # 先链接通用库

# 创建 process_b 程序
add_executable(process_b process_b.cpp)
target_link_libraries(process_b PRIVATE serialization) # 先链接通用库


# --- 如果开启了ZMQ，为目标添加ZMQ的头文件和链接库 ---
if(USE_ZMQ)
    # 为 a 和 b 添加头文件目录
    target_include_directories(process_a PRIVATE ${ZMQ_INCLUDE_DIR})
    target_include_directories(process_b PRIVATE ${ZMQ_INCLUDE_DIR})

    # 为 a 和 b 链接zmq库
    target_link_libraries(process_a PRIVATE libzmq)
    target_link_libraries(process_b PRIVATE libzmq)
endif()
