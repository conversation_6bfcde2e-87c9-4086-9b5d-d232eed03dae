{
    // CMake arugments to be passed during configuration
    "cmake.configureArgs": [
        "-G",
        "Visual Studio 15 2017",
        "-T",
        "host=x64",
        "-A",
        "x64",
        // Note: The vcpkg path is relative to the workspace folder for portability
        "-DCMAKE_TOOLCHAIN_FILE:FILEPATH=${workspaceFolder}/../zmq/vcpkg-2025.07.25/scripts/buildsystems/vcpkg.cmake"
    ],

    // Controls the build directory
    "cmake.buildDirectory": "${workspaceFolder}/build"
}
