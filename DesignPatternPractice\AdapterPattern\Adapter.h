#ifndef ADAPTER_H
#define ADAPTER_H
#include <string>
#include <iostream>
#include "ITarget.h"
#include "Adaptee.h"
#include <memory>

class Adapter : public ITarget{
public:
   explicit Adapter(std::unique_ptr<Adaptee> adaptee) : adaptee_(std::move(adaptee)){}

   std::string request() const override{
    return "Adapter: (TRANSLATED) " + adaptee_->specificRequest();
   }
private:
     std::unique_ptr<Adaptee> adaptee_;
};

#endif