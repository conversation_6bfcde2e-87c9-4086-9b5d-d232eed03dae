#ifndef SCHEDULER_H
#define SCHEDULER_H
#include <condition_variable>
// #include <functional>
#include <future>
#include <mutex>
#include <queue>
#include <thread>
#include <type_traits>
#include <vector>

class Scheduler {
public:
  explicit Scheduler(size_t thread_count = std::thread::hardware_concurrency())
      : stop(false) {
    for (size_t i = 0; i < thread_count; ++i) {
      workers.emplace_back([this] { work_loop(); });
    }
  }
  ~Scheduler() {
    {
      std::unique_lock<std::mutex> lock(queue_mutex);
      stop = true;
    }
    condition.notify_all();
    for (auto &worker : workers) {
      if (worker.joinable())
        worker.join();
    }
  }
  template <typename F, typename... Args>
  auto schedule(int priority, F &&f, Args &&...args)
      -> std::future<typename std::invoke_result_t<F, Args...>> {
    using return_type = typename std::invoke_result_t<F, Args...>;
    auto task = std::make_shared<std::packaged_task<return_type()>>(
        [f = std::forward<F>(f),
         args = std::make_tuple(std::forward<Args>(args)...)]() {
          return std::apply(f, args);
        });
    std::future<return_type> result = task->get_future();
    {
      std::unique_lock<std::mutex> lock(queue_mutex);
      if (stop)
        throw std::runtime_error("schedule on stopped scheduler");
      tasks.emplace(priority, [task]() { (*task)(); });
    }
    condition.notify_one();
    return result;
  }

private:
  struct TaskItem {
    int priority;
    std::function<void()> task;
    TaskItem() = default;
    TaskItem(int p, std::function<void()> func)
        : priority(p), task(std::move(func)) {}

    bool operator<(const TaskItem &other) const {
      return priority < other.priority;
    }
  };
  void work_loop() {
    while (true) {
      // TaskItem task_item;

      std::unique_lock<std::mutex> lock(queue_mutex);
      condition.wait(lock, [this] { return stop || !tasks.empty(); });
      if (stop && tasks.empty())
        return;
      TaskItem task_item = std::move(tasks.top());
      tasks.pop();

      try {
        task_item.task();
      } catch (...) {
      }
    }
  }

private:
  std::vector<std::thread> workers;
  std::condition_variable condition;
  std::priority_queue<TaskItem> tasks;
  bool stop;
  std::mutex queue_mutex;
};
#endif // SCHEDULER_H
