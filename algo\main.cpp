#include "catchRainwater.h"
#include <iostream>
#include <vector>

int main() {
  std::vector<std::vector<int>> test = {{3, 3, 3, 3, 3},
                                        {3, 1, 1, 1, 3},
                                        {3, 1, 0, 1, 3},
                                        {3, 1, 1, 1, 3},
                                        {3, 3, 3, 3, 3}};
  catchRainwater c1;
  int cap = c1.trap2D(test);
  std::cout << cap << std::endl;
  return 0;
}
