#include "Singleton.h" // 包含单例类的头文件

int main() {
    // 获取单例实例
    Singleton& s1 = Singleton::getInstance();
    s1.showMessages();

    // 再次获取单例实例
    Singleton& s2 = Singleton::getInstance();
    s2.showMessages();

    // 验证 s1 和 s2 是否是同一个实例
    if (&s1 == &s2) {
        std::cout << "s1 and s2 are the same instance." << std::endl;
    } else {
        std::cout << "s1 and s2 are different instances." << std::endl;
    }

    // 尝试通过构造函数创建实例 (会编译错误，因为构造函数是私有的)
    // Singleton s3; // 编译错误: 'Singleton::Singleton()' is private

    // 尝试通过拷贝创建实例 (会编译错误，因为拷贝构造函数被删除)
    // Singleton s4 = s1; // 编译错误: 'Singleton::Singleton(const Singleton&)' is deleted

    return 0;
}