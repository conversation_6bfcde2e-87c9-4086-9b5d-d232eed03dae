#include "../test.h"
#include <rxcpp/operators/rx-delay.hpp>

using namespace std::chrono;

SCENARIO("delay - never", "[delay][operators]"){
    GIVEN("a source"){
        auto sc = rxsc::make_test();
        auto so = rx::synchronize_in_one_worker(sc);
        auto w = sc.create_worker();
        const rxsc::test::messages<int> on;

        auto xs = sc.make_hot_observable({
            on.next(150, 1)
        });

        WHEN("values are delayed"){

            auto res = w.start(
                [so, xs]() {
                    return xs | rxo::delay(milliseconds(10), so);
                }
            );

            THEN("the output is empty"){
                auto required = std::vector<rxsc::test::messages<int>::recorded_type>();
                auto actual = res.get_observer().messages();
                REQUIRE(required == actual);
            }

            THEN("there was 1 subscription/unsubscription to the source"){
                auto required = rxu::to_vector({
                    on.subscribe(200, 1001)
                });
                auto actual = xs.subscriptions();
                REQUIRE(required == actual);
            }
        }
    }
}

SCENARIO("delay - empty", "[delay][operators]"){
    GIVEN("a source"){
        auto sc = rxsc::make_test();
        auto so = rx::synchronize_in_one_worker(sc);
        auto w = sc.create_worker();
        const rxsc::test::messages<int> on;

        auto xs = sc.make_hot_observable({
            on.next(150, 1),
            on.completed(250)
        });

        WHEN("values are delayed"){

            auto res = w.start(
                [so, xs]() {
                    return xs.delay(so, milliseconds(10));
                }
            );

            THEN("the output only contains complete message"){
                auto required = rxu::to_vector({
                    on.completed(260)
                });
                auto actual = res.get_observer().messages();
                REQUIRE(required == actual);
            }

            THEN("there was 1 subscription/unsubscription to the source"){
                auto required = rxu::to_vector({
                    on.subscribe(200, 250)
                });
                auto actual = xs.subscriptions();
                REQUIRE(required == actual);
            }

        }
    }
}

SCENARIO("delay - return", "[delay][operators]"){
    GIVEN("a source"){
        auto sc = rxsc::make_test();
        auto so = rx::synchronize_in_one_worker(sc);
        auto w = sc.create_worker();
        const rxsc::test::messages<int> on;

        auto xs = sc.make_hot_observable({
            on.next(150, 1),
            on.next(210, 2),
            on.next(240, 3),
            on.completed(300)
        });

        WHEN("values are delayed"){

            auto res = w.start(
                [so, xs]() {
                    return xs.delay(milliseconds(10), so);
                }
            );

            THEN("the output only contains delayed items sent while subscribed"){
                auto required = rxu::to_vector({
                    on.next(220, 2),
                    on.next(250, 3),
                    on.completed(310)
                });
                auto actual = res.get_observer().messages();
                REQUIRE(required == actual);
            }

            THEN("there was 1 subscription/unsubscription to the source"){
                auto required = rxu::to_vector({
                    on.subscribe(200, 300)
                });
                auto actual = xs.subscriptions();
                REQUIRE(required == actual);
            }

        }
    }
}

SCENARIO("delay - throw", "[delay][operators]"){
    GIVEN("a source"){
        auto sc = rxsc::make_test();
        auto so = rx::synchronize_in_one_worker(sc);
        auto w = sc.create_worker();
        const rxsc::test::messages<int> on;

        std::runtime_error ex("delay on_error from source");

        auto xs = sc.make_hot_observable({
            on.next(150, 1),
            on.error(250, ex)
        });

        WHEN("values are delayed"){

            auto res = w.start(
                [so, xs]() {
                    return xs.delay(milliseconds(10), so);
                }
            );

            THEN("the output only contains only error"){
                auto required = rxu::to_vector({
                    on.error(251, ex)
                });
                auto actual = res.get_observer().messages();
                REQUIRE(required == actual);
            }

            THEN("there was 1 subscription/unsubscription to the source"){
                auto required = rxu::to_vector({
                    on.subscribe(200, 250)
                });
                auto actual = xs.subscriptions();
                REQUIRE(required == actual);
            }

        }
    }
}

SCENARIO("delay provides 1 copy to internal state and 1 move from scheduler", "[delay][operators][copies]")
{
    GIVEN("a source")
    {
        auto                                      sc = rxsc::make_test();
        auto                                      so = rx::synchronize_in_one_worker(sc);
        auto                                      w  = sc.create_worker();
        const rxsc::test::messages<copy_verifier> on;
        copy_verifier                             verifier{};

        auto   xs    = sc.make_cold_observable({on.next(150, verifier), on.completed(300)});
        size_t count = verifier.get_copy_count();

        WHEN("start")
        {
            auto res = w.start([so, xs]() { return xs.delay(milliseconds(10), so); });

            THEN("no extra copies")
            {
                // 1 copy to internal state
                CHECK(verifier.get_copy_count() - count == 1);
                // 1 move from scheduler and 1 move for test_notification
                REQUIRE(verifier.get_move_count() == 2);
            }
        }
    }
}

