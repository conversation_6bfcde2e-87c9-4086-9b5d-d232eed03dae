{"version": "0.2.0", "configurations": [{"name": "Launch pool", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}/build/pool/Debug/pool.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "console": "externalTerminal", "visualizerFile": "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\908359f89f52cbe27e84da714e1769a8\\tonka3000.qtvsctools\\qt.natvis.xml"}, {"name": "Launch Scheduler", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}/build/Scheduler/Debug/Scheduler.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "console": "externalTerminal", "visualizerFile": "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\908359f89f52cbe27e84da714e1769a8\\tonka3000.qtvsctools\\qt.natvis.xml"}, {"name": "Launch FactoryPattern", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}/build/DesignPatternPractice/Debug/FactoryPattern.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "console": "externalTerminal", "visualizerFile": "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\908359f89f52cbe27e84da714e1769a8\\tonka3000.qtvsctools\\qt.natvis.xml"}, {"name": "Launch SingletonPattern", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}/build/DesignPatternPractice/Debug/SingletonPattern.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "console": "externalTerminal", "visualizerFile": "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\908359f89f52cbe27e84da714e1769a8\\tonka3000.qtvsctools\\qt.natvis.xml"}, {"name": "Launch AdapterPattern", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}/build/DesignPatternPractice/Debug/AdapterPattern.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "console": "externalTerminal", "visualizerFile": "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\908359f89f52cbe27e84da714e1769a8\\tonka3000.qtvsctools\\qt.natvis.xml"}, {"name": "Launch ObserverPattern", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}/build/DesignPatternPractice/Debug/ObserverPattern.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "console": "externalTerminal", "visualizerFile": "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\908359f89f52cbe27e84da714e1769a8\\tonka3000.qtvsctools\\qt.natvis.xml"}, {"name": "Launch DecoratorPattern", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}/build/DesignPatternPractice/Debug/DecoratorPattern.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "console": "externalTerminal", "visualizerFile": "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\908359f89f52cbe27e84da714e1769a8\\tonka3000.qtvsctools\\qt.natvis.xml"}]}