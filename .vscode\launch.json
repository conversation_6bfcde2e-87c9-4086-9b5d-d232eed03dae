{"version": "0.2.0", "configurations": [{"name": "Launch process_a", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}/build/callbackproto/Debug/process_a.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}/build/callbackproto/Debug", "environment": [], "console": "integratedTerminal", "preLaunchTask": "build", "visualizerFile": "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\908359f89f52cbe27e84da714e1769a8\\tonka3000.qtvsctools\\qt.natvis.xml"}, {"name": "Launch process_b", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}/build/callbackproto/Debug/process_b.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}/build/callbackproto/Debug", "environment": [], "console": "integratedTerminal", "preLaunchTask": "build", "visualizerFile": "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\908359f89f52cbe27e84da714e1769a8\\tonka3000.qtvsctools\\qt.natvis.xml"}], "compounds": [{"name": "Run ZMQ Demo (b then a)", "configurations": ["Launch process_b", "Launch process_a"]}]}