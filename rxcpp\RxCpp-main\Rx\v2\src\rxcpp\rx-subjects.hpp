// Copyright (c) Microsoft Open Technologies, Inc. All rights reserved. See License.txt in the project root for license information.

#pragma once

#if !defined(RXCPP_RX_SCHEDULER_SUBJECTS_HPP)
#define RXCPP_RX_SCHEDULER_SUBJECTS_HPP

#include "rx-includes.hpp"

namespace rxcpp {

namespace subjects {

}
namespace rxsub=subjects;

}

#include "subjects/rx-subject.hpp"
#include "subjects/rx-behavior.hpp"
#include "subjects/rx-replaysubject.hpp"
#include "subjects/rx-synchronize.hpp"

#endif
