#include "common.h"
#include "serialization.h"
#include <zmq.hpp>
#include <iostream>

// 进程B的回调函数：处理User数据
void handle_user(const User& user) {
    std::cout << "进程B回调处理：收到用户 id=" << user.id << ", name=" << user.name << std::endl;
}

// 进程B：接收数据，反序列化后触发回调
int main() {
    zmq::context_t context(1);
    zmq::socket_t socket(context, zmq::socket_type::pull);
    socket.bind("tcp://*:5555"); // 绑定端口等待进程A的数据
    
    // 注册回调
    UserCallback callback = handle_user;
    
    // 接收数据
    zmq::message_t msg;
    socket.recv(msg, zmq::recv_flags::none);
    std::vector<char> data(static_cast<char*>(msg.data()), static_cast<char*>(msg.data()) + msg.size());
    
    // 反序列化
    User user = deserialize(data);
    
    // 触发回调处理数据
    callback(user);
    
    return 0;
}
