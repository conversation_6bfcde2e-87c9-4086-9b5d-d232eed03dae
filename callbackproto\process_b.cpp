#include "common.h"
#include "serialization.h"
#include <iostream>
#include <vector>

#ifdef USE_ZMQ
#include <zmq.h>
#else
#include <fstream>
#endif

// Process B callback function: handle User data
void handle_user(const User& user) {
    std::cout << "Callback triggered! Processing received data..." << std::endl;
    std::cout << "  -> User ID: " << user.id << std::endl;
    std::cout << "  -> User Name: " << user.name << std::endl << std::flush;
}

// Process B: receive data, deserialize and trigger callback
int main() {
    std::cout << "--- Process B (Receiver) ---" << std::endl << std::flush;

    // Register callback
    UserCallback callback = handle_user;
    std::vector<char> data;

#ifdef USE_ZMQ
    std::cout << "[Mode]: ZeroMQ (tcp://*:5555)" << std::endl << std::flush;
    std::cout << "Waiting for data from Process A..." << std::endl << std::flush;

    void* context = zmq_ctx_new();
    void* socket = zmq_socket(context, ZMQ_PULL);
    zmq_bind(socket, "tcp://*:5555");

    // Receive data
    char buffer[1024]; // Simple buffer
    int size = zmq_recv(socket, buffer, sizeof(buffer), 0);
    if (size > 0) {
        data.assign(buffer, buffer + size);
        std::cout << "Data received successfully." << std::endl << std::flush;
    } else {
        std::cerr << "Error: zmq_recv failed." << std::endl << std::flush;
        zmq_close(socket);
        zmq_ctx_destroy(context);
        return 1;
    }

    // Cleanup
    zmq_close(socket);
    zmq_ctx_destroy(context);
#else
    std::cout << "[Mode]: File I/O (user_data.bin)" << std::endl << std::flush;
    std::cout << "(Note: Run process_a first to generate the data file)" << std::endl << std::flush;
    std::cout << "Attempting to read from 'user_data.bin'..." << std::endl << std::flush;

    std::ifstream file("user_data.bin", std::ios::binary);
    if (file.is_open()) {
        file.seekg(0, std::ios::end);
        size_t size = file.tellg();
        file.seekg(0, std::ios::beg);

        data.resize(size);
        file.read(data.data(), size);
        file.close();
        std::cout << "Data file read successfully." << std::endl << std::flush;
    } else {
        std::cerr << "Error: Could not open 'user_data.bin'." << std::endl << std::flush;
        std::cerr << "Please run process_a first to create it." << std::endl << std::flush;
        return 1;
    }
#endif

    // Deserialize
    User user = deserialize(data);

    // Trigger callback to handle data
    callback(user);

    return 0;
}