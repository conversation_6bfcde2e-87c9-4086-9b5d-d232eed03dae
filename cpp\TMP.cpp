// 递归模板：计算n! = n * (n-1)!
template <int N>
struct Factorial {
    static const int value = N * Factorial<N-1>::value;
};

// 特化模板：终止条件（0! = 1）
template <>
struct Factorial<0> {
    static const int value = 1;
};

#include <iostream>

// 编译期计算5!，结果在编译时确定
int main() {
    constexpr int result = Factorial<5>::value; // 编译期计算：5! = 120
    std::cout << "Factorial<5>::value test = " << result << std::endl;
    std::cout << "This was calculated at compile time!" << std::endl;
    return 0;
}