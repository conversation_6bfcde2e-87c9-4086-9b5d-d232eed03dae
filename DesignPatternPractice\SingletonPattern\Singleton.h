#ifndef SINGLETON_H
#define SINGLETON_H
#include <iostream>

class Singleton{
    private:
    Singleton(){
        std::cout <<"Singleton instance created"<<std::endl;
    }
    Singleton(const Singleton&) = delete;
    Singleton& operator =(const Singleton&) = delete;
    public:
    static Singleton& getInstance(){
        static Singleton instance;
        return instance;
    }
    void showMessages() const{
        std::cout<< "hello from Singleton" <<std::endl;
    }
};


#endif