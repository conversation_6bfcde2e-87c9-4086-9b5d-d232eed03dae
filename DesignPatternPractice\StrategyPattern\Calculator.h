#ifndef CALCULATOR_H
#define CALCULATOR_H

#include "IStrategy.h"
#include <memory>

// 上下文，它维护一个对策略对象的引用
class Calculator {
private:
    std::unique_ptr<IStrategy> strategy_;

public:
    // 构造函数，可以不设置策略
    Calculator() = default;

    // 允许在运行时切换策略
    void setStrategy(std::unique_ptr<IStrategy> strategy) {
        strategy_ = std::move(strategy);
    }

    // 执行计算，将工作委托给当前的策略对象
    int executeStrategy(int a, int b) const {
        if (strategy_) {
            return strategy_->execute(a, b);
        }
        // 如果没有设置策略，可以返回一个错误码或抛出异常
        return 0; 
    }
};

#endif // CALCULATOR_H
