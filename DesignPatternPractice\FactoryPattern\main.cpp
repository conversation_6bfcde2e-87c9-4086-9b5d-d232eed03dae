#include <iostream>
#include <memory>
#include "ITransport.h"
#include "ItransportFactory.h"
#include "TruckFactory.h"
#include "ShipFactory.h"

void clientCode(ITransportFactory& factory){
    std::unique_ptr<ITransport> transport = factory.createTransport();
    transport->deliver();

}

int main(){
    std::cout << "App: Launched with the TruckFactory." << std::endl;
    TruckFactory truckFactory;
    clientCode(truckFactory);
    ShipFactory shipFactory;
    clientCode(shipFactory);
    return 0;
}