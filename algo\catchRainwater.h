﻿#ifndef CATCHRAINWATER_H
#define CATCHRAINWATER_H
#include <queue>
#include <vector>
class catchRainwater {
public:
  int trap(std::vector<int> &height) {
    int left = 0, right = height.size() - 1;
    int left_max = 0, right_max = 0;
    int res = 0;
    while (left < right) {
      if (height[left] < height[right]) {
        if (height[left] >= left_max) {
          left_max = height[left];
        } else {
          res += left_max - height[left];
        }
        left++;
      } else {
        if (height[right] >= right_max) {
          right_max = height[right];
        } else {
          res += right_max - height[right];
        }
        right++;
      }
    }
    return res;
  }
  int trap2D(std::vector<std::vector<int>> &heightMap) {
    if (heightMap.empty())
      return 0;
    int m = heightMap.size(), n = heightMap[0].size();
    std::priority_queue<std::pair<int, std::pair<int, int>>,
                        std::vector<std::pair<int, std::pair<int, int>>>,
                        std::greater<>>
        pq;
    std::vector<std::vector<bool>> visited(m, std::vector<bool>(n, false));
    for (int i = 0; i < m; ++i) {
      for (int j = 0; j < n; ++j) {
        if (i == 0 || i == m - 1 || j == 0 || j == n - 1) {
          pq.push({heightMap[i][j], {i, j}});
          visited[i][j] = true;
        }
      }
    }
    int res = 0;
    int curr_max = 0;
    const int dirs[4][2] = {{-1, 0}, {1, 0}, {0, -1}, {0, 1}};
    while (!pq.empty()) {
      auto [h, pos] = pq.top();
      pq.pop();
      int x = pos.first, y = pos.second;
      curr_max = std::max(curr_max, h);

      for (auto [dx, dy] : dirs) {
        int nx = x + dx, ny = y + dy;
        if (nx < 0 || nx >= m || ny < 0 || ny >= n || visited[nx][ny]) {
          continue;
        }
        if (heightMap[nx][ny] < curr_max) {
          res += curr_max - heightMap[nx][ny];
        }
        pq.push({heightMap[nx][ny], {nx, ny}});
        visited[nx][ny] = true;
      }
    }
    return res;
  }
};

#endif
